import React, { useState, useEffect, useMemo } from 'react'
import { Modal } from '@instructure/ui-modal'
import { View } from '@instructure/ui-view'
import { Flex } from '@instructure/ui-flex'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { IconButton } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import { Spinner } from '@instructure/ui-spinner'
import CanvasSelect from '@canvas/instui-bindings/react/Select'
import { IconArrowOpenStartLine, IconArrowOpenEndLine } from '@instructure/ui-icons'
import type { FacultyUser } from '../types'
// import styles from './FacultyCalendarModal.module.css'

interface TimeSlot {
  id: string
  datetime: string
  formatted_time: string
  is_available: boolean
  is_booked: boolean
  has_pending?: boolean
  is_past?: boolean
  faculty_id: string
  faculty_time_slot_id?: string
  created_at: string
  updated_at: string
}

interface FacultyCalendarModalProps {
  isOpen: boolean
  onClose: () => void
  faculty: FacultyUser | null
  onTimeSlotSelect: (datetime: string, facultyTimeSlotId?: string) => void
  selectedDateTime?: string
}

const FacultyCalendarModal: React.FC<FacultyCalendarModalProps> = ({
  isOpen,
  onClose,
  faculty,
  onTimeSlotSelect,
  selectedDateTime
}) => {
  const [currentWeek, setCurrentWeek] = useState(new Date())
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth())
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([])
  const [loading, setLoading] = useState(false)

  // Generate week dates - memoized to prevent infinite re-renders
  const weekDates = useMemo(() => {
    const getWeekDates = (date: Date) => {
      const week = []
      const startOfWeek = new Date(date)
      const day = startOfWeek.getDay()
      const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1) // Monday as first day
      startOfWeek.setDate(diff)

      for (let i = 0; i < 7; i++) {
        const day = new Date(startOfWeek)
        day.setDate(startOfWeek.getDate() + i)
        week.push(day)
      }
      return week
    }
    return getWeekDates(currentWeek)
  }, [currentWeek])

  // Navigation functions
  const goToPreviousWeek = () => {
    const prevWeek = new Date(currentWeek)
    prevWeek.setDate(currentWeek.getDate() - 7)
    setCurrentWeek(prevWeek)
  }

  const goToNextWeek = () => {
    const nextWeek = new Date(currentWeek)
    nextWeek.setDate(currentWeek.getDate() + 7)
    setCurrentWeek(nextWeek)
  }

  const goToSelectedMonth = () => {
    const newDate = new Date(selectedYear, selectedMonth, 1)
    setCurrentWeek(newDate)
  }

  // Fetch time slots for the current week
  useEffect(() => {
    if (!faculty || !isOpen) return

    const fetchTimeSlots = async () => {
      setLoading(true)

      try {
        const startDate = weekDates[0].toISOString().split('T')[0]
        const endDate = weekDates[6].toISOString().split('T')[0]
        const url = `/consultation_requests/faculty/${faculty.id}/time_slots?start_date=${startDate}&end_date=${endDate}`

        const response = await fetch(url)

        if (response.ok) {
          const data = await response.json()
          setTimeSlots(data.time_slots || [])
        } else {
          const errorText = await response.text()
          console.error('Failed to fetch time slots. Status:', response.status)
          console.error('Error response:', errorText)

          // For testing/development - generate some sample slots if API fails
          const sampleSlots = generateSampleTimeSlots(faculty.id, weekDates)
          setTimeSlots(sampleSlots)
        }
      } catch (error) {
        console.error('Error fetching time slots:', error)

        // For testing/development - generate some sample slots if API fails
        const sampleSlots = generateSampleTimeSlots(faculty.id, weekDates)
        setTimeSlots(sampleSlots)
      } finally {
        setLoading(false)
      }
    }

    fetchTimeSlots()
  }, [faculty, currentWeek, isOpen, weekDates])

  // Helper function to generate sample time slots for testing/development
  const generateSampleTimeSlots = (facultyId: string, dates: Date[]) => {
    const slots: TimeSlot[] = []

    dates.forEach((date, dateIndex) => {
      // Skip weekends for sample data
      if (date.getDay() === 0 || date.getDay() === 6) return

      // Create a 2-hour consultation slot from 2:30 PM to 4:20 PM (4 x 30-minute slots)
      const multiHourSlotId = `faculty-slot-${dateIndex}-multi`
      const startTime = new Date(date)
      startTime.setHours(14, 30, 0) // 2:30 PM

      // Generate 4 consecutive 30-minute slots for the 2-hour consultation
      for (let i = 0; i < 4; i++) {
        const slotTime = new Date(startTime)
        slotTime.setMinutes(startTime.getMinutes() + (i * 30))

        slots.push({
          id: `${multiHourSlotId}-${i}`,
          datetime: slotTime.toISOString(),
          formatted_time: slotTime.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
          }),
          is_available: true,
          is_booked: false,
          faculty_id: facultyId,
          faculty_time_slot_id: multiHourSlotId, // Same ID for all slots in the group
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
      }

      // Add a single morning slot (9:00 AM)
      const morningSlot = new Date(date)
      morningSlot.setHours(9, 0, 0)

      slots.push({
        id: `sample-morning-${date.toISOString()}`,
        datetime: morningSlot.toISOString(),
        formatted_time: '9:00 AM',
        is_available: true,
        is_booked: false,
        faculty_id: facultyId,
        faculty_time_slot_id: `faculty-slot-${dateIndex}-morning`, // Single slot ID
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })

      // Add a single evening slot (5:00 PM)
      const eveningSlot = new Date(date)
      eveningSlot.setHours(17, 0, 0)

      slots.push({
        id: `sample-evening-${date.toISOString()}`,
        datetime: eveningSlot.toISOString(),
        formatted_time: '5:00 PM',
        is_available: true,
        is_booked: date.getDate() % 2 === 0, // Every other day is booked
        faculty_id: facultyId,
        faculty_time_slot_id: `faculty-slot-${dateIndex}-evening`, // Single slot ID
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    })

    return slots
  }

  // Generate time slots for display (7 AM to 7 PM)
  const generateTimeSlots = () => {
    const slots = []
    for (let hour = 7; hour <= 19; hour++) {
      slots.push(`${hour.toString().padStart(2, '0')}:00`)
      if (hour < 19) {
        slots.push(`${hour.toString().padStart(2, '0')}:30`)
      }
    }
    return slots
  }

  const timeSlotHours = generateTimeSlots()

  // Group consecutive time slots for the same faculty time slot
  const getSlotGroups = (date: Date) => {
    const dateStr = date.toISOString().split('T')[0]
    const slotsForDate = timeSlots.filter(slot => {
      const slotDate = new Date(slot.datetime)
      return slotDate.toISOString().split('T')[0] === dateStr
    })

    // Group by faculty_time_slot_id
    const groupedSlots = slotsForDate.reduce((groups, slot) => {
      const key = slot.faculty_time_slot_id || slot.id
      if (!groups[key]) {
        groups[key] = []
      }
      groups[key].push(slot)
      return groups
    }, {} as Record<string, TimeSlot[]>)

    // Sort slots within each group by datetime
    Object.keys(groupedSlots).forEach(key => {
      groupedSlots[key].sort((a, b) => new Date(a.datetime).getTime() - new Date(b.datetime).getTime())
    })

    return groupedSlots
  }

  // Check if a time slot is the start of a group
  const isSlotGroupStart = (date: Date, slot: TimeSlot): boolean => {
    const slotGroups = getSlotGroups(date)
    const group = slotGroups[slot.faculty_time_slot_id || slot.id]

    if (!group || group.length <= 1) return true

    const slotDate = new Date(slot.datetime)
    const firstSlotInGroup = group[0]
    const firstSlotDate = new Date(firstSlotInGroup.datetime)

    return slotDate.getTime() === firstSlotDate.getTime()
  }

  // Get the height span for a slot group
  const getSlotGroupSpan = (date: Date, slot: TimeSlot): number => {
    const slotGroups = getSlotGroups(date)
    const group = slotGroups[slot.faculty_time_slot_id || slot.id]

    return group ? group.length : 1
  }

  // Check if a time slot should be hidden (part of a group but not the start)
  const shouldHideSlot = (date: Date, slot: TimeSlot): boolean => {
    return !isSlotGroupStart(date, slot)
  }

  // Get slots for specific date and time
  const getSlotsForDateTime = (date: Date, time: string) => {
    const dateStr = date.toISOString().split('T')[0]
    const [hour, minute] = time.split(':')

    const matchingSlots = timeSlots.filter(slot => {
      const slotDate = new Date(slot.datetime)
      const matches = (
        slotDate.toISOString().split('T')[0] === dateStr &&
        slotDate.getHours() === parseInt(hour) &&
        slotDate.getMinutes() === parseInt(minute)
      )

      return matches
    })

    return matchingSlots
  }

  // Handle time slot selection
  const handleTimeSlotClick = (slot: TimeSlot) => {
    if (slot.is_available && !slot.is_booked && !slot.has_pending && !slot.is_past) {
      onTimeSlotSelect(slot.datetime, slot.faculty_time_slot_id || slot.id)
    }
  }

  // Format date for display
  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    })
  }

  // Generate month options
  const monthOptions = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]

  // Generate year options (current year ± 2)
  const currentYear = new Date().getFullYear()
  const yearOptions = []
  for (let year = currentYear - 1; year <= currentYear + 2; year++) {
    yearOptions.push(year)
  }

  return (
    <Modal
      open={isOpen}
      onDismiss={onClose}
      size="fullscreen"
      label="Faculty Consultation Calendar"
      shouldCloseOnDocumentClick={false}
    >
      <Modal.Header>
        <Heading level="h3">
          Select Consultation Time - {faculty?.name}
        </Heading>
      </Modal.Header>

      <Modal.Body>
        <div style={{ padding: '1rem', height: '100%', display: 'flex', flexDirection: 'column' }}>
          {/* Calendar Navigation */}
          <Flex justifyItems="space-between" alignItems="center" margin="0 0 medium 0">
            <Flex.Item>
              <Flex gap="small" alignItems="center">
                <IconButton
                  screenReaderLabel="Previous week"
                  onClick={goToPreviousWeek}
                  size="small"
                >
                  <IconArrowOpenStartLine />
                </IconButton>

                <Text weight="bold" size="large">
                  {weekDates[0].toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                </Text>

                <IconButton
                  screenReaderLabel="Next week"
                  onClick={goToNextWeek}
                  size="small"
                >
                  <IconArrowOpenEndLine />
                </IconButton>
              </Flex>
            </Flex.Item>

            <Flex.Item>
              <Flex gap="small" alignItems="center">
                <View as="div" margin="0 small 0 0">
                  <CanvasSelect
                    id="month-select"
                    label="Month"
                    value={selectedMonth.toString()}
                    onChange={(_e, value) => {
                      setSelectedMonth(parseInt(value))
                    }}
                  >
                    {monthOptions.map((month, index) => (
                      <CanvasSelect.Option key={index} id={index.toString()} value={index.toString()}>
                        {month}
                      </CanvasSelect.Option>
                    ))}
                  </CanvasSelect>
                </View>

                <View as="div" margin="0 small 0 0">
                  <CanvasSelect
                    id="year-select"
                    label="Year"
                    value={selectedYear.toString()}
                    onChange={(_e, value) => setSelectedYear(parseInt(value))}
                  >
                    {yearOptions.map(year => (
                      <CanvasSelect.Option key={year} id={year.toString()} value={year.toString()}>
                        {year.toString()}
                      </CanvasSelect.Option>
                    ))}
                  </CanvasSelect>
                </View>

                <Button onClick={goToSelectedMonth} size="small">
                  Go
                </Button>
              </Flex>
            </Flex.Item>
          </Flex>

          {/* Calendar Grid */}
          <div style={{
            border: '1px solid #ccc',
            borderRadius: '0.375rem',
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            flex: 1
          }}>
            {/* Week Header */}
            <View as="div" background="secondary" borderWidth="0 0 small 0">
              <Flex>
                <Flex.Item size="80px" padding="small" textAlign="center">
                  <Text weight="bold" size="small">Time</Text>
                </Flex.Item>
                {weekDates.map((date, index) => (
                  <Flex.Item key={index} shouldGrow padding="small" textAlign="center" width={120}>
                    <Text weight="bold" size="small">
                      {formatDate(date)}
                    </Text>
                  </Flex.Item>
                ))}
              </Flex>
            </View>

            {/* Time Slots Grid */}
            {loading ? (
              <div style={{
                padding: '2rem',
                textAlign: 'center',
                height: '100%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Spinner renderTitle="Loading time slots..." />
              </div>
            ) : (
              <div style={{ height: '100%', overflowY: 'auto', flex: 1 }}>
                {timeSlotHours.map((time, timeIndex) => (
                  <View key={timeIndex} as="div" borderWidth="0 0 small 0" minHeight="40px">
                    <Flex alignItems="center">
                      <Flex.Item size="80px" padding="x-small" textAlign="center">
                        <Text size="small" weight="normal">{time}</Text>
                      </Flex.Item>
                      {weekDates.map((date, dateIndex) => {
                        const slotsAtTime = getSlotsForDateTime(date, time)
                        const hasSlot = slotsAtTime.length > 0
                        const slot = slotsAtTime[0] // Take first slot if multiple
                        const isPast = new Date(`${date.toISOString().split('T')[0]}T${time}:00`) < new Date()
                        const isSelected = selectedDateTime === slot?.datetime

                        // Determine slot status and appearance
                        const getSlotStatus = () => {
                          if (!hasSlot) return { text: isPast ? "Past" : "No Slot", color: "secondary" as const, textColor: "secondary" as const, cssClass: "past", disabled: true }
                          if (isSelected) return { text: "Selected", color: "primary" as const, textColor: "primary-inverse" as const, cssClass: "selected", disabled: false }
                          if (slot.is_past) return { text: "Expired", color: "secondary" as const, textColor: "secondary" as const, cssClass: "past", disabled: true }
                          if (slot.has_pending) return { text: "Pending", color: "secondary" as const, textColor: "secondary" as const, cssClass: "pending", disabled: true }
                          if (slot.is_booked) return { text: "Booked", color: "danger" as const, textColor: "primary-inverse" as const, cssClass: "booked", disabled: true }
                          if (slot.is_available) return { text: "Available", color: "success" as const, textColor: "primary-inverse" as const, cssClass: "available", disabled: false }
                          return { text: "Unavailable", color: "secondary" as const, textColor: "secondary" as const, cssClass: "unavailable", disabled: true }
                        }

                        const slotStatus = getSlotStatus()

                        // Check if this slot should be hidden (part of a group but not the start)
                        const shouldHide = hasSlot && shouldHideSlot(date, slot)
                        const groupSpan = hasSlot ? getSlotGroupSpan(date, slot) : 1

                        console.log('-----------------FACULTYCLENDARMODAL slot:')

                        return (
                          <Flex.Item
                            key={dateIndex}
                            shouldGrow
                            padding="x-small"
                            textAlign="center"
                            width={120}
                          >
                            <View as="div" position="relative" width="100%">
                              {hasSlot && !shouldHide ? (
                                <button
                                  className={`slot-button ${slotStatus.cssClass}`}
                                  onClick={() => handleTimeSlotClick(slot)}
                                  disabled={slotStatus.disabled}
                                  style={{
                                    width: '100%',
                                    height: groupSpan > 1 ? `calc(${groupSpan} * 40px + ${groupSpan - 1}px)` : '32px',
                                    borderRadius: '4px',
                                    fontSize: '0.75rem',
                                    fontWeight: '500',
                                    cursor: slotStatus.disabled ? 'default' : 'pointer',
                                    transition: 'all 0.2s ease',
                                    zIndex: groupSpan > 1 ? 10 : 1,
                                    position: groupSpan > 1 ? 'absolute' : 'relative',
                                    top: groupSpan > 1 ? '1px' : 0,
                                    left: groupSpan > 1 ? '1px' : 0,
                                    right: groupSpan > 1 ? '1px' : 0,
                                    margin: 0,
                                    padding: 0,
                                    boxSizing: 'border-box',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    // Color styling based on status
                                    backgroundColor: slotStatus.cssClass === 'available' ? '#d4edda' :
                                                   slotStatus.cssClass === 'booked' ? '#ef4444' :
                                                   slotStatus.cssClass === 'selected' ? '#3b82f6' :
                                                   slotStatus.cssClass === 'pending' ? '#f59e0b' :
                                                   '#e5e7eb',
                                    color: slotStatus.cssClass === 'available' ? '#155724' :
                                           slotStatus.cssClass === 'booked' ? 'white' :
                                           slotStatus.cssClass === 'selected' ? 'white' :
                                           slotStatus.cssClass === 'pending' ? 'white' :
                                           '#9ca3af',
                                    border: slotStatus.cssClass === 'available' ? '1px solid #c3e6cb' :
                                           slotStatus.cssClass === 'selected' ? '1px solid #93c5fd' :
                                           '1px solid transparent',
                                    // Enhanced styling for multi-hour slots
                                    ...(groupSpan > 1 && {
                                      border: slotStatus.cssClass === 'available' ? '2px solid #c3e6cb' :
                                             slotStatus.cssClass === 'selected' ? '2px solid #93c5fd' :
                                             '2px solid rgba(255, 255, 255, 0.2)',
                                      boxShadow: slotStatus.cssClass === 'selected' ?
                                                '0 0 0 3px #93c5fd, 0 2px 8px rgba(0, 0, 0, 0.15)' :
                                                '0 2px 8px rgba(0, 0, 0, 0.15)',
                                      // Use the same background colors as single slots, no gradients
                                      backgroundColor: slotStatus.cssClass === 'available' ? '#d4edda' :
                                                      slotStatus.cssClass === 'booked' ? '#ef4444' :
                                                      slotStatus.cssClass === 'selected' ? '#3b82f6' :
                                                      slotStatus.cssClass === 'pending' ? '#f59e0b' :
                                                      '#e5e7eb'
                                    })
                                  }}
                                >
                                  <div style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    height: '100%'
                                  }}>
                                    <div>{slotStatus.text}</div>
                                    {groupSpan > 1 && slot.faculty_time_slot_id && (
                                      <div style={{
                                        fontSize: '10px',
                                        opacity: 0.9,
                                        marginTop: '2px',
                                        fontWeight: 'normal',
                                        lineHeight: 1.2
                                      }}>
                                        {new Date(slot.datetime).toLocaleTimeString('en-US', {
                                          hour: 'numeric',
                                          minute: '2-digit',
                                          hour12: true
                                        })} - {new Date(new Date(slot.datetime).getTime() + (groupSpan * 30 * 60 * 1000)).toLocaleTimeString('en-US', {
                                          hour: 'numeric',
                                          minute: '2-digit',
                                          hour12: true
                                        })}
                                      </div>
                                    )}
                                  </div>
                                </button>
                              ) : hasSlot && shouldHide ? (
                                // Render empty space for hidden slots that are part of a group
                                <View as="div" height="32px" width="100%" />
                              ) : (
                                <View
                                  as="div"
                                  height="32px"
                                  background={isPast ? "secondary" : "primary"}
                                  borderRadius="small"
                                  textAlign="center"
                                  padding="x-small"
                                  width="100%"
                                >
                                  <Flex justifyItems="center" alignItems="center" height="100%">
                                    <Flex.Item textAlign='center' width={120}>
                                      <Text size="x-small" color={isPast ? "secondary" : "primary"}>
                                        No slots
                                      </Text>
                                    </Flex.Item>
                                  </Flex>
                                </View>
                              )}
                            </View>
                          </Flex.Item>
                        )
                      })}
                    </Flex>
                  </View>
                ))}
              </div>
            )}
          </div>

          {/* Legend */}
          <Flex gap="medium" margin="medium 0 0 0" justifyItems="center" wrap="wrap">
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="success" borderRadius="small" />
                <Text size="small">Available</Text>
              </Flex>
            </Flex.Item>
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="danger" borderRadius="small" />
                <Text size="small">Booked</Text>
              </Flex>
            </Flex.Item>
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="secondary" borderRadius="small" />
                <Text size="small">Pending</Text>
              </Flex>
            </Flex.Item>
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="secondary" borderRadius="small" />
                <Text size="small">Expired</Text>
              </Flex>
            </Flex.Item>
            <Flex.Item>
              <Flex gap="x-small" alignItems="center">
                <View as="div" width="16px" height="16px" background="primary" borderRadius="small" />
                <Text size="small">Selected</Text>
              </Flex>
            </Flex.Item>
          </Flex>
        </div>
      </Modal.Body>

      <Modal.Footer>
        <Button onClick={onClose}>Close</Button>
      </Modal.Footer>
    </Modal>
  )
}

export default FacultyCalendarModal